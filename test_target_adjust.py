#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试target_adjust.py...")

try:
    # 尝试导入并编译
    with open('target_adjust.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    print("正在编译代码...")
    compile(code, 'target_adjust.py', 'exec')
    print("✅ 编译成功！")
    
    # 尝试执行导入部分
    print("正在测试导入...")
    exec("""
from maix import image, display, app, time, camera, touchscreen
from maix import uart as maix_uart
import cv2
import numpy as np
import os
import threading
print("✅ 导入成功！")
""")
    
    print("✅ 所有测试通过！")
    
except SyntaxError as e:
    print(f"❌ 语法错误:")
    print(f"   行号: {e.lineno}")
    print(f"   错误: {e.msg}")
    if e.text:
        print(f"   代码: {e.text.strip()}")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    with open('target_adjust.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    compile(code, 'target_adjust.py', 'exec')
    print("✅ 语法检查通过！")
    
except SyntaxError as e:
    print(f"❌ 语法错误:")
    print(f"   文件: {e.filename}")
    print(f"   行号: {e.lineno}")
    print(f"   错误: {e.msg}")
    print(f"   代码: {e.text}")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")

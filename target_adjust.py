# target_adjust.py - 准心坐标调节工具
# 基于MaixPy框架的触摸屏准心坐标调节工具

from maix import image, display, app, time, camera, touchscreen, uart
import cv2
import numpy as np
import os
import threading

# --------------------------- 串口控制器（模仿HSV调参.py） ---------------------------
class SerialController:
    """串口控制器 - 模仿HSV调参.py的实现方式"""

    def __init__(self):
        self.serial = None
        self.is_initialized = False
        print("[初始化] 串口控制器已创建")

    def init(self, device="/dev/ttyS0", baudrate=115200):
        """初始化串口"""
        try:
            self.serial = uart.UART(device, baudrate)
            self.is_initialized = True
            print(f"[成功] 串口初始化成功 - {device}:{baudrate}")
            return True
        except Exception as e:
            print(f"[错误] 串口初始化失败: {e}")
            self.is_initialized = False
            return False

    def send_packet(self, packet_data):
        """发送数据包"""
        if not self.is_initialized or not self.serial:
            print("[错误] 串口未初始化，无法发送数据")
            return False

        try:
            if isinstance(packet_data, bytes):
                # 直接发送字节数据
                self.serial.write(packet_data)
                return True
            else:
                # 转换为字符串并发送
                self.serial.write_str(str(packet_data))
                return True
        except Exception as e:
            print(f"[错误] 串口发送失败: {e}")
            return False

    def close(self):
        """关闭串口"""
        if self.serial:
            try:
                self.serial.close()
                print("[关闭] 串口已关闭")
            except:
                pass
        self.is_initialized = False
        self.serial = None

# 激光控制参数
LASER_CONTROL_ENABLED = True     # 激光控制功能使能
SEND_ENABLED = True              # 数据包发送使能标志（默认启用，无需外部指令）

# 全局串口控制器
serial_controller = None

# --------------------------- 激光控制函数 ---------------------------
def send_laser_control_packet(identifier, current_diff_x=0, current_diff_y=0):
    """发送激光控制数据包

    数据包格式：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC

    Args:
        identifier: 激光控制标识符(0x15开启, 0x25关闭)
        current_diff_x, current_diff_y: 当前误差值(可选)
    """
    global serial_controller

    # 检查发送使能标志
    if not SEND_ENABLED or not LASER_CONTROL_ENABLED:
        return False

    try:
        # 取绝对值并限制在16位范围内
        abs_x = min(abs(current_diff_x), 65535)
        abs_y = min(abs(current_diff_y), 65535)

        # 分解为高低八位
        x_high = (abs_x >> 8) & 0xFF
        x_low = abs_x & 0xFF
        y_high = (abs_y >> 8) & 0xFF
        y_low = abs_y & 0xFF

        # 构建数据包
        packet = bytes([0x78, identifier, x_high, x_low, y_high, y_low, 0xFC])

        # 使用串口控制器发送数据包
        if serial_controller and serial_controller.is_initialized:
            success = serial_controller.send_packet(packet)
            if success:
                action = "开启激光" if identifier == 0x15 else "关闭激光"
                print(f"📡 发送激光控制指令: {action} (0x{identifier:02X})")
                return True
            else:
                print("❌ 串口发送失败")
                return False
        else:
            print("❌ 串口未初始化，无法发送激光控制指令")
            return False

    except Exception as e:
        print(f"❌ 发送激光控制指令失败: {e}")
        return False

def process_uart_command():
    """处理串口接收到的hex指令（简化版本）

    指令说明：
    - 0xFF: 停止串口发送
    - 0x00: 开始发送指令
    """
    global SEND_ENABLED

    # 简化版本：由于使用了HSV调参.py的串口模式，暂时保留此函数以兼容
    # 实际的串口接收功能可以根据需要后续添加
    pass

# --------------------------- 准心坐标调节工具类 ---------------------------
class TargetAdjustTool:
    """准心坐标调节工具类

    提供准心坐标调节功能，支持X、Y坐标独立调节
    支持触摸屏交互和实时预览
    支持配置文件保存和加载
    """
    
    def __init__(self, initial_x=164, initial_y=122):
        """初始化准心坐标调节工具
        
        Args:
            initial_x (int): 初始X坐标，默认164
            initial_y (int): 初始Y坐标，默认122
        """
        self.target_x = initial_x  # 当前X坐标
        self.target_y = initial_y  # 当前Y坐标
        self.step = 1  # 坐标调节步长
        self.min_x = 0  # 最小X坐标
        self.max_x = 320  # 最大X坐标（基于摄像头分辨率）
        self.min_y = 0  # 最小Y坐标
        self.max_y = 240  # 最大Y坐标（基于摄像头分辨率）

        # 触摸相关参数
        self.button_positions = {}  # 存储按钮位置信息
        self.last_touch_time = 0  # 上次触摸时间
        self.touch_debounce = 200  # 防抖时间(ms)

        # 调试和性能监控参数
        self.debug_enabled = True  # 调试模式开关
        self.frame_count = 0  # 帧计数器
        self.last_fps_time = 0  # 上次FPS计算时间
        self.fps_interval = 5000  # FPS报告间隔(ms)
        self.total_coordinate_changes = 0  # 坐标变化总次数

        # 临时消息显示参数
        self.temp_message = None  # 当前临时消息
        self.temp_message_end = 0  # 消息结束时间

        # 激光控制状态
        self.laser_enabled = False  # 激光是否开启
        self.laser_state = "off"    # 激光状态: off, on
        self.laser_toggle_time = 0  # 上次激光切换时间

        print(f"准心坐标调节工具初始化完成，初始坐标: ({self.target_x}, {self.target_y})")
        if self.debug_enabled:
            print("调试模式已启用 - 目标：准心坐标调节")
    
    def adjust_coordinate(self, axis, delta):
        """调节坐标
        
        Args:
            axis (str): 坐标轴（'x' 或 'y'）
            delta (int): 坐标变化量（正数增加，负数减少）
            
        Returns:
            tuple: 调节后的坐标 (x, y)
        """
        if axis == 'x':
            old_x = self.target_x
            self.target_x = max(self.min_x, min(self.max_x, self.target_x + delta))
            
            if old_x != self.target_x:
                self.total_coordinate_changes += 1
                action = "增加" if delta > 0 else "减少"
                print(f"X坐标调节: {old_x} -> {self.target_x} ({action})")
                if self.debug_enabled:
                    print(f"累计调节次数: {self.total_coordinate_changes}")
                    
        elif axis == 'y':
            old_y = self.target_y
            self.target_y = max(self.min_y, min(self.max_y, self.target_y + delta))
            
            if old_y != self.target_y:
                self.total_coordinate_changes += 1
                action = "增加" if delta > 0 else "减少"
                print(f"Y坐标调节: {old_y} -> {self.target_y} ({action})")
                if self.debug_enabled:
                    print(f"累计调节次数: {self.total_coordinate_changes}")

        return (self.target_x, self.target_y)
    
    def get_coordinates(self):
        """获取当前坐标
        
        Returns:
            tuple: 当前坐标 (x, y)
        """
        return (self.target_x, self.target_y)
    
    def set_coordinates(self, x, y):
        """设置坐标
        
        Args:
            x (int): 新的X坐标
            y (int): 新的Y坐标
            
        Returns:
            tuple: 设置后的坐标 (x, y)
        """
        old_x, old_y = self.target_x, self.target_y
        self.target_x = max(self.min_x, min(self.max_x, x))
        self.target_y = max(self.min_y, min(self.max_y, y))
        
        if old_x != self.target_x or old_y != self.target_y:
            print(f"坐标设置: ({old_x}, {old_y}) -> ({self.target_x}, {self.target_y})")
        
        return (self.target_x, self.target_y)

    def toggle_laser(self):
        """切换激光开关状态

        Returns:
            bool: 激光是否开启
        """
        current_time = time.ticks_ms()

        # 防抖动检查
        if current_time - self.laser_toggle_time < 500:  # 500ms防抖
            return self.laser_enabled

        self.laser_toggle_time = current_time

        if self.laser_enabled:
            # 关闭激光
            self.laser_enabled = False
            self.laser_state = "off"
            success = send_laser_control_packet(0x25, 0, 0)  # 发送关闭指令
            if success:
                print("🔴 激光已关闭")
                self.show_temp_message("LASER OFF", 1000)
            else:
                print("❌ 激光关闭指令发送失败")
                self.show_temp_message("LASER OFF FAILED", 1000)
        else:
            # 开启激光
            self.laser_enabled = True
            self.laser_state = "on"
            success = send_laser_control_packet(0x15, 0, 0)  # 发送开启指令
            if success:
                print("🟢 激光已开启")
                self.show_temp_message("LASER ON", 1000)
            else:
                print("❌ 激光开启指令发送失败")
                self.show_temp_message("LASER ON FAILED", 1000)

        return self.laser_enabled

    def set_laser_state(self, enabled):
        """设置激光状态

        Args:
            enabled (bool): 是否开启激光

        Returns:
            bool: 设置是否成功
        """
        if enabled == self.laser_enabled:
            return True  # 状态已经是目标状态

        return self.toggle_laser()

    def show_temp_message(self, message, duration_ms):
        """显示临时消息
        
        Args:
            message (str): 要显示的消息
            duration_ms (int): 显示持续时间（毫秒）
        """
        self.temp_message = message
        self.temp_message_end = time.ticks_ms() + duration_ms

    def draw_ui(self, img, disp_width, disp_height):
        """绘制用户界面：显示准心预览和坐标信息

        Args:
            img: 输入图像
            disp_width: 显示宽度
            disp_height: 显示高度

        Returns:
            np.ndarray: 显示图像
        """
        try:
            # 计算图像显示区域（预留底部按钮区域）
            img_height = disp_height - 60

            # 调整输入图像尺寸
            if img is not None:
                resized_img = cv2.resize(img, (disp_width, img_height))
            else:
                # 如果没有输入图像，创建黑色背景
                resized_img = np.zeros((img_height, disp_width, 3), dtype=np.uint8)

            # 创建显示图像
            display_img = np.zeros((disp_height, disp_width, 3), dtype=np.uint8)

            # 放置图像
            display_img[:img_height, :] = resized_img

            # 绘制准心位置预览
            # 将准心坐标映射到显示区域
            preview_x = int(self.target_x * disp_width / 320)
            preview_y = int(self.target_y * img_height / 240)
            
            # 确保预览坐标在显示区域内
            preview_x = max(5, min(disp_width - 5, preview_x))
            preview_y = max(5, min(img_height - 5, preview_y))
            
            # 绘制准心十字线
            cv2.line(display_img, (preview_x - 10, preview_y), (preview_x + 10, preview_y), (255, 0, 255), 2)
            cv2.line(display_img, (preview_x, preview_y - 10), (preview_x, preview_y + 10), (255, 0, 255), 2)
            cv2.circle(display_img, (preview_x, preview_y), 3, (255, 0, 255), -1)

            # 添加标题
            cv2.putText(display_img, "Target Adjust", (10, 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

            # 显示当前坐标
            coord_text = f"X:{self.target_x} Y:{self.target_y}"
            cv2.putText(display_img, coord_text, (10, img_height - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

            # 显示激光状态
            laser_status = "ON" if self.laser_enabled else "OFF"
            laser_color = (0, 255, 0) if self.laser_enabled else (0, 0, 255)
            cv2.putText(display_img, f"Laser: {laser_status}", (10, img_height - 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, laser_color, 2)

            # 显示发送状态
            send_status = "ON" if SEND_ENABLED else "OFF"
            send_color = (0, 255, 0) if SEND_ENABLED else (0, 0, 255)
            cv2.putText(display_img, f"Send: {send_status}", (10, img_height - 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, send_color, 2)

            # 显示临时消息（如保存/加载/重置结果）
            if self.temp_message and time.ticks_ms() < self.temp_message_end:
                # 计算消息显示位置（屏幕中央）
                text_x = disp_width // 2 - 80
                text_y = disp_height // 2

                # 根据消息内容设置颜色
                if any(keyword in self.temp_message for keyword in ["SAVED", "LOADED", "RESET"]):
                    color = (0, 255, 0)  # 绿色表示成功
                else:
                    color = (0, 0, 255)  # 红色表示失败

                # 绘制临时消息
                cv2.putText(display_img, self.temp_message, (text_x, text_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 1.5, color, 3)

            elif self.temp_message:
                # 消息过期，清除
                self.temp_message = None

            return display_img

        except Exception as e:
            print(f"界面绘制错误: {e}")
            # 返回空白图像
            return np.zeros((disp_height, disp_width, 3), dtype=np.uint8)

    def draw_buttons(self, img, disp_width, disp_height):
        """绘制控制按钮

        Args:
            img: 要绘制按钮的图像
            disp_width: 显示宽度
            disp_height: 显示高度

        Returns:
            dict: 按钮位置信息字典
        """
        try:
            # 定义按钮位置和尺寸
            button_height = 40
            button_width = 60
            y_pos = disp_height - 50

            # 按钮位置信息
            button_positions = {}

            # 绘制坐标显示
            coord_text = f'X:{self.target_x} Y:{self.target_y}'
            cv2.putText(img, coord_text, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # X坐标调节按钮
            # X减少按钮 (X-)
            x_dec_x = disp_width // 2 - 140
            x_dec_y = y_pos - 20
            cv2.rectangle(img, (x_dec_x, x_dec_y),
                         (x_dec_x + button_width, x_dec_y + button_height),
                         (0, 0, 255), 2)  # 红色边框
            cv2.putText(img, "X-", (x_dec_x + 18, x_dec_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            button_positions['x_decrease'] = (x_dec_x, x_dec_y, button_width, button_height)

            # X增加按钮 (X+)
            x_inc_x = disp_width // 2 - 70
            x_inc_y = y_pos - 20
            cv2.rectangle(img, (x_inc_x, x_inc_y),
                         (x_inc_x + button_width, x_inc_y + button_height),
                         (0, 255, 0), 2)  # 绿色边框
            cv2.putText(img, "X+", (x_inc_x + 15, x_inc_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            button_positions['x_increase'] = (x_inc_x, x_inc_y, button_width, button_height)

            # Y坐标调节按钮
            # Y减少按钮 (Y-)
            y_dec_x = disp_width // 2 + 10
            y_dec_y = y_pos - 20
            cv2.rectangle(img, (y_dec_x, y_dec_y),
                         (y_dec_x + button_width, y_dec_y + button_height),
                         (255, 0, 0), 2)  # 蓝色边框
            cv2.putText(img, "Y-", (y_dec_x + 18, y_dec_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
            button_positions['y_decrease'] = (y_dec_x, y_dec_y, button_width, button_height)

            # Y增加按钮 (Y+)
            y_inc_x = disp_width // 2 + 80
            y_inc_y = y_pos - 20
            cv2.rectangle(img, (y_inc_x, y_inc_y),
                         (y_inc_x + button_width, y_inc_y + button_height),
                         (255, 255, 0), 2)  # 黄色边框
            cv2.putText(img, "Y+", (y_inc_x + 15, y_inc_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
            button_positions['y_increase'] = (y_inc_x, y_inc_y, button_width, button_height)

            # 绘制退出按钮
            exit_x = disp_width - 80
            exit_y = 10
            exit_width = 70
            exit_height = 30
            cv2.rectangle(img, (exit_x, exit_y),
                         (exit_x + exit_width, exit_y + exit_height),
                         (255, 255, 255), 2)  # 白色边框
            cv2.putText(img, "Exit", (exit_x + 15, exit_y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            button_positions['exit'] = (exit_x, exit_y, exit_width, exit_height)

            # 功能按钮尺寸（增大以便于触摸）
            func_button_width = 70
            func_button_height = 40
            func_button_x = disp_width - 80  # 右侧位置

            # 绘制保存按钮 (Save)
            save_y = 50
            cv2.rectangle(img, (func_button_x, save_y),
                         (func_button_x + func_button_width, save_y + func_button_height),
                         (255, 0, 0), 2)  # 蓝色边框
            cv2.putText(img, "Save", (func_button_x + 15, save_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            button_positions['save'] = (func_button_x, save_y, func_button_width, func_button_height)

            # 绘制加载按钮 (Load)
            load_y = save_y + func_button_height + 15
            cv2.rectangle(img, (func_button_x, load_y),
                         (func_button_x + func_button_width, load_y + func_button_height),
                         (255, 255, 0), 2)  # 青色边框
            cv2.putText(img, "Load", (func_button_x + 15, load_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            button_positions['load'] = (func_button_x, load_y, func_button_width, func_button_height)

            # 绘制重置按钮 (Reset)
            reset_y = load_y + func_button_height + 15
            cv2.rectangle(img, (func_button_x, reset_y),
                         (func_button_x + func_button_width, reset_y + func_button_height),
                         (0, 165, 255), 2)  # 橙色边框
            cv2.putText(img, "Reset", (func_button_x + 10, reset_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 165, 255), 2)
            button_positions['reset'] = (func_button_x, reset_y, func_button_width, func_button_height)

            # 绘制激光控制按钮 (Laser)
            laser_y = reset_y + func_button_height + 15
            laser_color = (0, 255, 0) if self.laser_enabled else (128, 128, 128)  # 绿色表示开启，灰色表示关闭
            laser_text = "Laser" if not self.laser_enabled else "Laser"
            cv2.rectangle(img, (func_button_x, laser_y),
                         (func_button_x + func_button_width, laser_y + func_button_height),
                         laser_color, 2)
            cv2.putText(img, laser_text, (func_button_x + 8, laser_y + 28),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, laser_color, 2)
            button_positions['laser'] = (func_button_x, laser_y, func_button_width, func_button_height)

            return button_positions

        except Exception as e:
            print(f"按钮绘制错误: {e}")
            return {}

    def setup_buttons(self, disp_width, disp_height):
        """设置按钮位置信息

        Args:
            disp_width: 显示宽度
            disp_height: 显示高度
        """
        # 定义按钮位置和尺寸
        button_height = 40
        button_width = 60
        y_pos = disp_height - 50

        # 功能按钮尺寸和位置
        func_button_width = 70
        func_button_height = 40
        func_button_x = disp_width - 80

        self.button_positions = {
            'x_decrease': (disp_width//2 - 140, y_pos - 20, button_width, button_height),
            'x_increase': (disp_width//2 - 70, y_pos - 20, button_width, button_height),
            'y_decrease': (disp_width//2 + 10, y_pos - 20, button_width, button_height),
            'y_increase': (disp_width//2 + 80, y_pos - 20, button_width, button_height),
            'exit': (disp_width - 80, 10, 70, 30),
            'save': (func_button_x, 50, func_button_width, func_button_height),
            'load': (func_button_x, 105, func_button_width, func_button_height),
            'reset': (func_button_x, 160, func_button_width, func_button_height),
            'laser': (func_button_x, 215, func_button_width, func_button_height)
        }

        print(f"按钮位置设置完成: {self.button_positions}")

    def is_in_button(self, x, y, button_name):
        """检查触摸点是否在指定按钮内

        Args:
            x: 触摸点x坐标
            y: 触摸点y坐标
            button_name: 按钮名称

        Returns:
            bool: 是否在按钮内
        """
        if button_name not in self.button_positions:
            return False

        bx, by, bw, bh = self.button_positions[button_name]
        return bx <= x <= bx + bw and by <= y <= by + bh

    def handle_touch(self, x, y, pressed, current_time):
        """处理触摸事件

        Args:
            x: 触摸点x坐标
            y: 触摸点y坐标
            pressed: 是否按下
            current_time: 当前时间(ms)

        Returns:
            str or None: 触摸结果
        """
        if not pressed:
            return None

        # 防抖动检查
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        # 检查各个按钮
        for button_name in self.button_positions:
            if self.is_in_button(x, y, button_name):
                self.last_touch_time = current_time
                print(f"按钮触摸: {button_name} at ({x}, {y})")
                return button_name

        return None

    def save_coordinates_to_file(self, file_path="/root/target.txt"):
        """保存当前坐标到文件

        Args:
            file_path (str): 保存文件的路径，默认为"/root/target.txt"

        Returns:
            bool: 保存是否成功
        """
        try:
            with open(file_path, 'w') as f:
                f.write(f"target_x={self.target_x}\n")
                f.write(f"target_y={self.target_y}\n")

            print(f"[成功] 坐标已保存到 {file_path}")
            self.show_temp_message("SAVED", 1000)
            return True

        except (IOError, OSError) as e:
            print(f"[错误] 保存坐标失败: {e}")
            self.show_temp_message("SAVE FAILED", 1000)
            return False
        except Exception as e:
            print(f"[错误] 保存过程发生未知错误: {e}")
            self.show_temp_message("SAVE FAILED", 1000)
            return False

    def load_coordinates_from_file(self, file_path="/root/target.txt"):
        """从文件加载坐标

        Args:
            file_path (str): 文件路径，默认为"/root/target.txt"

        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(file_path):
                print(f"[错误] 加载失败，文件不存在: {file_path}")
                self.show_temp_message("LOAD FAILED", 1000)
                return False

            target_x = None
            target_y = None

            with open(file_path, 'r') as f:
                lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        if key == 'target_x':
                            try:
                                x_value = int(value)
                                if self.min_x <= x_value <= self.max_x:
                                    target_x = x_value
                                else:
                                    print(f"[错误] X坐标超出范围 ({self.min_x}-{self.max_x}): {x_value}")
                            except ValueError:
                                print(f"[错误] X坐标格式错误: {value}")

                        elif key == 'target_y':
                            try:
                                y_value = int(value)
                                if self.min_y <= y_value <= self.max_y:
                                    target_y = y_value
                                else:
                                    print(f"[错误] Y坐标超出范围 ({self.min_y}-{self.max_y}): {y_value}")
                            except ValueError:
                                print(f"[错误] Y坐标格式错误: {value}")

            # 检查是否成功读取了坐标
            if target_x is not None and target_y is not None:
                self.target_x = target_x
                self.target_y = target_y
                print(f"[成功] 已从 {file_path} 加载坐标: ({self.target_x}, {self.target_y})")
                self.show_temp_message("LOADED", 1000)
                return True
            else:
                print(f"[错误] 文件中未找到有效的坐标参数")
                self.show_temp_message("LOAD FAILED", 1000)
                return False

        except (IOError, OSError) as e:
            print(f"[错误] 加载坐标失败: {e}")
            self.show_temp_message("LOAD FAILED", 1000)
            return False
        except Exception as e:
            print(f"[错误] 加载过程发生未知错误: {e}")
            self.show_temp_message("LOAD FAILED", 1000)
            return False

    def reset_coordinates(self):
        """重置坐标到默认值

        Returns:
            bool: 重置是否成功
        """
        try:
            self.target_x = 164  # 重置到默认值
            self.target_y = 122  # 重置到默认值
            print("[成功] 坐标已重置到默认值: (164, 122)")
            self.show_temp_message("RESET", 1000)
            return True
        except Exception as e:
            print(f"[错误] 重置坐标失败: {e}")
            self.show_temp_message("RESET FAILED", 1000)
            return False

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 设备初始化（遵循阈值存储.py模式）
    try:
        print("开始初始化准心坐标调节工具...")

        # 初始化显示屏
        disp = display.Display()
        print(f"显示屏初始化成功，尺寸: {disp.width()}x{disp.height()}")

        # 初始化摄像头
        cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
        print("摄像头初始化成功")

        # 初始化触摸屏
        ts = touchscreen.TouchScreen()
        print("触摸屏初始化成功")

        # 初始化串口控制器（模仿HSV调参.py）
        serial_controller = SerialController()
        if serial_controller.init("/dev/ttyS0", 115200):
            print("串口控制器初始化成功")
        else:
            print("串口控制器初始化失败")

        # 初始化准心坐标调节工具
        tool = TargetAdjustTool()

        # 设置按钮位置
        tool.setup_buttons(disp.width(), disp.height())

        print("准心坐标调节工具初始化完成！")
        print("使用说明：")
        print("- 实时显示准心位置预览")
        print("- 点击 'X-' 按钮减少X坐标")
        print("- 点击 'X+' 按钮增加X坐标")
        print("- 点击 'Y-' 按钮减少Y坐标")
        print("- 点击 'Y+' 按钮增加Y坐标")
        print("- 点击 'Save' 按钮保存当前坐标到文件")
        print("- 点击 'Load' 按钮从文件加载坐标")
        print("- 点击 'Reset' 按钮重置坐标到默认值")
        print("- 点击 'Exit' 按钮退出程序")

        # 主循环
        while not app.need_exit():
            try:
                # 处理串口指令
                process_uart_command()

                # 获取摄像头图像
                img = cam.read()
                if img is None:
                    continue
                img_cv = image.image2cv(img, ensure_bgr=False, copy=False)

                # 绘制界面
                display_img = tool.draw_ui(img_cv, disp.width(), disp.height())

                # 绘制按钮
                button_positions = tool.draw_buttons(display_img, disp.width(), disp.height())

                # 处理触摸输入
                if ts.available():
                    touch_data = ts.read()
                    if len(touch_data) >= 3:
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        current_time = time.ticks_ms()

                        action = tool.handle_touch(touch_x, touch_y, pressed, current_time)

                        if action:
                            if action == "x_decrease":
                                tool.adjust_coordinate('x', -tool.step)
                            elif action == "x_increase":
                                tool.adjust_coordinate('x', tool.step)
                            elif action == "y_decrease":
                                tool.adjust_coordinate('y', -tool.step)
                            elif action == "y_increase":
                                tool.adjust_coordinate('y', tool.step)
                            elif action == "save":
                                tool.save_coordinates_to_file()
                            elif action == "load":
                                tool.load_coordinates_from_file()
                            elif action == "reset":
                                tool.reset_coordinates()
                            elif action == "laser":
                                tool.toggle_laser()
                            elif action == "exit":
                                print("用户请求退出")
                                break

                # 显示图像
                img_show = image.cv2image(display_img, bgr=True, copy=False)
                disp.show(img_show)

            except Exception as e:
                print(f"主循环错误: {e}")
                continue

    except Exception as e:
        print(f"初始化失败: {e}")

    print("准心坐标调节工具已退出")
